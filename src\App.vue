<template>
  <canvas ref="canvas"></canvas>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import * as THREE from 'three';
  //导入轨道控制器
  import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
  import { GUI } from 'three/addons/libs/lil-gui.module.min.js';
  import * as TEWWN from 'three/addons/libs/tween.module.js';

  //创建场景
  const scene = new THREE.Scene();

  //创建相机
  const camera = new THREE.PerspectiveCamera(
    45, //视角
    window.innerWidth / window.innerHeight, //宽高比
    0.1, //近裁剪面
    1000 //远裁剪面
  );
  //设置相机位置
  camera.position.z = 15;
  camera.position.y = 2;
  camera.position.x = 2;
  //设置相机看向的位置
  camera.lookAt(0, 0, 0);

  //添加世界坐标辅助器
  const axesHelper = new THREE.AxesHelper(5);
  scene.add(axesHelper);

  //创建3个不同颜色的球
  const sphereGeometry = new THREE.SphereGeometry(1, 32, 32);
  //创建3个不同的材质
  const material1 = new THREE.MeshBasicMaterial({ color: 0xff6633 });
  //创建3个球体网格
  const sphere1 = new THREE.Mesh(sphereGeometry, material1);
  //设置球体位置
  sphere1.position.set(-4, 0, 0);
  //添加到场景
  scene.add(sphere1);

  const tween = new Tween(sphere1.position);
  tween.to({ x: 4 }, 1000).onUpdate(() => {
    console.log(sphere1.position.x);
  });
  //循环次数
  // tween.repeat(2);
  tween.repeat(Infinity);
  //循环往复
  tween.yoyo(true);
  //延迟运行
  tween.delay(1000);
  //缓动函数
  tween.easing(TEWWN.Easing.Quadratic.InOut);
  //启动动画
  tween.start();

  //创建渲染器（延后到 onMounted，通过模板里的 canvas 进行渲染）
  let renderer: THREE.WebGLRenderer;
  //将渲染器添加到页面中
  const canvas = ref<HTMLCanvasElement | null>(null);

  // 让动画循环可访问控制器
  let controls: OrbitControls | null = null;

  //创建GUI
  const enventObj = {
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.body.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    },
  };
  const gui = new GUI();
  //添加按钮
  gui.add(enventObj, 'toggleFullscreen').name('全屏切换');

  //渲染函数
  function animate() {
    requestAnimationFrame(animate);
    // 每帧更新控制器（阻尼/自动旋转）
    controls?.update();
    //渲染
    renderer.render(scene, camera);
    tween.update();
  }

  onMounted(() => {
    if (canvas.value) {
      renderer = new THREE.WebGLRenderer({ canvas: canvas.value });
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
      renderer.setSize(window.innerWidth, window.innerHeight);

      //添加轨道控制器
      controls = new OrbitControls(camera, renderer.domElement);

      animate();
    }
  });
</script>

<style scoped>
  canvas {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
</style>
